import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { target } = await request.json();
    
    if (!target) {
      return NextResponse.json({ 
        success: false, 
        error: '目标地址不能为空' 
      }, { status: 400 });
    }

    // 服务端HTTP延迟测试
    const latency = await serverSideLatencyTest(target);
    
    return NextResponse.json({
      success: true,
      latency,
      target,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('服务端ping测试失败:', error);
    return NextResponse.json({ 
      success: false, 
      error: '网络测试失败',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 });
  }
}

// 服务端延迟测试函数
async function serverSideLatencyTest(target: string): Promise<number> {
  try {
    // 确保URL格式正确
    let testUrl = target;
    if (!testUrl.startsWith('http://') && !testUrl.startsWith('https://')) {
      testUrl = `https://${testUrl}`;
    }

    const startTime = Date.now();
    
    // 使用fetch进行HTTP请求
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8秒超时

    const response = await fetch(testUrl, {
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; PingTool/1.0)',
      },
    });

    clearTimeout(timeoutId);
    const endTime = Date.now();
    
    if (response.ok || response.status < 500) {
      return endTime - startTime;
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('请求超时');
    }
    throw error;
  }
}

// 支持GET请求用于健康检查
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    message: 'Ping API is running',
    timestamp: Date.now()
  });
}
