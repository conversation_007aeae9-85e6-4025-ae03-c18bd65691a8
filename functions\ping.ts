// Cloudflare Workers 函数用于服务端网络测试
// 部署到 Cloudflare Pages Functions

interface Env {
  // 可以在这里定义环境变量
}

interface PingRequest {
  target: string;
  method?: 'http' | 'tcp';
  timeout?: number;
}

interface PingResponse {
  success: boolean;
  latency?: number;
  target: string;
  method: string;
  timestamp: number;
  error?: string;
  location?: string;
}

export const onRequestPost: PagesFunction<Env> = async (context) => {
  const { request } = context;
  
  // 设置CORS头
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
  };

  // 处理预检请求
  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body: PingRequest = await request.json();
    const { target, method = 'http', timeout = 8000 } = body;

    if (!target) {
      return Response.json(
        { 
          success: false, 
          error: '目标地址不能为空',
          target: '',
          method,
          timestamp: Date.now()
        },
        { status: 400, headers: corsHeaders }
      );
    }

    // 执行网络测试
    const result = await performNetworkTest(target, method, timeout);
    
    // 添加Cloudflare边缘位置信息
    const cfLocation = request.cf?.colo || 'Unknown';
    result.location = cfLocation;

    return Response.json(result, { headers: corsHeaders });
  } catch (error) {
    console.error('网络测试失败:', error);
    return Response.json(
      {
        success: false,
        error: '服务器内部错误',
        target: '',
        method: 'http',
        timestamp: Date.now()
      },
      { status: 500, headers: corsHeaders }
    );
  }
};

// 执行网络测试
async function performNetworkTest(target: string, method: string, timeout: number): Promise<PingResponse> {
  const startTime = Date.now();
  
  try {
    // 确保URL格式正确
    let testUrl = target;
    if (!testUrl.startsWith('http://') && !testUrl.startsWith('https://')) {
      testUrl = `https://${target}`;
    }

    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    let response: Response;
    
    if (method === 'http') {
      // HTTP延迟测试
      response = await fetch(testUrl, {
        method: 'HEAD',
        signal: controller.signal,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; CloudflarePingTool/1.0)',
        },
      });
    } else {
      // 默认使用HTTP方法（Cloudflare Workers不支持原始TCP）
      response = await fetch(testUrl, {
        method: 'HEAD',
        signal: controller.signal,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; CloudflarePingTool/1.0)',
        },
      });
    }

    clearTimeout(timeoutId);
    const endTime = Date.now();
    const latency = endTime - startTime;

    if (response.ok || response.status < 500) {
      return {
        success: true,
        latency,
        target,
        method,
        timestamp: Date.now()
      };
    } else {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        target,
        method,
        timestamp: Date.now()
      };
    }
  } catch (error) {
    const endTime = Date.now();
    const latency = endTime - startTime;
    
    if (error instanceof Error && error.name === 'AbortError') {
      return {
        success: false,
        error: '请求超时',
        target,
        method,
        timestamp: Date.now()
      };
    }

    // 对于某些错误，我们仍然可以返回延迟时间
    if (latency < timeout) {
      return {
        success: true,
        latency,
        target,
        method,
        timestamp: Date.now()
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      target,
      method,
      timestamp: Date.now()
    };
  }
}

// 处理GET请求 - 健康检查
export const onRequestGet: PagesFunction<Env> = async (context) => {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
  };

  const cfLocation = context.request.cf?.colo || 'Unknown';
  
  return Response.json(
    {
      status: 'ok',
      message: 'Cloudflare Workers Ping API is running',
      location: cfLocation,
      timestamp: Date.now()
    },
    { headers: corsHeaders }
  );
};
