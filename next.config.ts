import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 配置静态导出以支持 Cloudflare Pages
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  },
  // 禁用构建缓存以避免大文件问题
  experimental: {
    webpackBuildWorker: false,
  },
  // 配置 webpack 以减少输出大小
  webpack: (config, { isServer }) => {
    // 禁用缓存
    config.cache = false;
    return config;
  },
};

export default nextConfig;
