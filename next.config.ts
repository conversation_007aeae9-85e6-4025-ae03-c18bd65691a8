import type { NextConfig } from "next";

// 检查是否为静态导出模式
const isStaticExport = process.env.BUILD_MODE === 'static';

const nextConfig: NextConfig = {
  // 根据环境决定是否使用静态导出
  ...(isStaticExport && {
    output: 'export',
    trailingSlash: true,
  }),
  images: {
    unoptimized: true
  },
  // 禁用构建缓存以避免大文件问题
  experimental: {
    webpackBuildWorker: false,
  },
  // 配置 webpack 以减少输出大小
  webpack: (config, { isServer }) => {
    // 禁用缓存
    config.cache = false;
    return config;
  },
};

export default nextConfig;
