<div align="center">

# 🌐 Ping 工具 - 网络连通性测试平台

**一个现代化的网络延迟测试工具，提供中国地图可视化的网络连通性监控**

[![Next.js](https://img.shields.io/badge/Next.js-15.3.5-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.4.17-38B2AC?style=for-the-badge&logo=tailwind-css)](https://tailwindcss.com/)
[![Cloudflare Pages](https://img.shields.io/badge/Cloudflare-Pages-F38020?style=for-the-badge&logo=cloudflare)](https://pages.cloudflare.com/)

[🚀 在线体验](https://ping.pages.dev) | [📖 使用文档](#使用指南) | [🛠️ 本地部署](#本地开发)

</div>

---

## ✨ 功能特性

<table>
<tr>
<td width="50%">

### 🎯 核心功能
- 🗺️ **中国地图可视化** - 基于真实地理数据的网络延迟热力图
- 📊 **全国节点测试** - 覆盖34个省市自治区的网络连通性测试
- ⚡ **实时延迟监控** - 毫秒级精度的网络延迟检测
- 📈 **趋势图表展示** - 网络延迟变化趋势可视化分析

</td>
<td width="50%">

### 🎨 用户体验
- 🌓 **深色/浅色主题** - 完美适配不同使用场景
- 📱 **响应式设计** - 完美适配桌面端、平板和移动设备
- 🔢 **访问统计** - 实时显示页面访问次数
- 🎭 **动画效果** - 流畅的交互动画和过渡效果

</td>
</tr>
</table>

## 🖼️ 界面预览

<div align="center">

### 🌞 浅色主题
<img src="public/img/256.jpeg" alt="Light Theme Interface" width="800" style="border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin: 20px 0;">

### 🌙 深色主题
<img src="public/img/2535.jpeg" alt="Dark Theme Interface" width="800" style="border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin: 20px 0;">

*支持浅色/深色主题切换的现代化界面设计*

</div>

## 🛠️ 技术栈

<div align="center">

| 分类 | 技术 | 版本 | 描述 |
|------|------|------|------|
| **前端框架** | Next.js | 15.3.5 | React 全栈框架 |
| **开发语言** | TypeScript | 5.0+ | 类型安全的 JavaScript |
| **样式框架** | Tailwind CSS | 3.4.17 | 原子化 CSS 框架 |
| **地图组件** | react-simple-maps | 3.0.0 | SVG 地图组件库 |
| **图表库** | Recharts | 3.0.2 | React 图表组件库 |
| **图标库** | Lucide React | 0.525.0 | 现代化图标库 |
| **地理数据** | china-map-geojson | 1.0.4 | 中国地图 GeoJSON 数据 |
| **图表引擎** | ECharts | 5.6.0 | 数据可视化图表库 |

</div>

## 🚀 快速开始

### 📋 环境要求

- **Node.js** >= 18.0.0
- **npm** >= 8.0.0 或 **yarn** >= 1.22.0

### 💻 本地开发

```bash
# 1. 克隆项目
git clone https://github.com/wob-21/ping.git
cd ping

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev

# 4. 打开浏览器访问
# http://localhost:3000
```

### 🏗️ 构建部署

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 📚 使用指南

### 🎯 基本使用

1. **输入目标** - 在输入框中输入要测试的域名或IP地址
2. **开始测试** - 点击"开始"按钮启动全国网络延迟测试
3. **查看结果** - 在地图上查看各省市的网络延迟情况
4. **切换视图** - 支持地图视图和网格视图两种展示方式

### 🎨 界面功能

- **🌓 主题切换** - 右上角按钮切换深色/浅色主题
- **📊 视图切换** - 地图视图和网格视图随意切换
- **📈 实时图表** - 底部显示网络延迟趋势图表
- **🔢 访问统计** - 实时显示页面访问次数

## 🌐 部署指南

### Cloudflare Pages 部署 (推荐)

1. Fork 本项目到你的 GitHub 账户
2. 登录 [Cloudflare Pages](https://pages.cloudflare.com)
3. 连接你的 GitHub 仓库
4. 配置构建设置：
   - **构建命令**: `npm run build`
   - **输出目录**: `out`
5. 点击部署，等待构建完成

### Vercel 部署

1. 登录 [Vercel](https://vercel.com)
2. 导入 GitHub 项目
3. 自动检测 Next.js 项目并部署

## 📁 项目结构

```
ping/
├── 📁 src/
│   ├── 📁 app/                 # Next.js App Router
│   │   ├── 📄 layout.tsx       # 全局布局
│   │   ├── 📄 page.tsx         # 首页
│   │   └── 📄 globals.css      # 全局样式
│   ├── 📁 components/          # React 组件
│   │   ├── 📄 ChinaMap.tsx     # 中国地图组件
│   │   ├── 📄 PingTool.tsx     # Ping 工具主组件
│   │   ├── 📄 PingChart.tsx    # 延迟图表组件
│   │   ├── 📄 NetworkMonitor.tsx # 网络监控组件
│   │   └── 📄 VisitCounter.tsx # 访问计数器
│   └── 📁 types/               # TypeScript 类型定义
├── 📁 public/                  # 静态资源
│   ├── 📁 img/                 # 图片资源
│   │   ├── 🖼️ 256.jpeg         # Logo 图片
│   │   └── 🖼️ 2535.jpeg        # 界面预览图
│   └── 📄 favicon.ico          # 网站图标
├── 📄 package.json             # 项目配置
├── 📄 next.config.ts           # Next.js 配置
├── 📄 tailwind.config.js       # Tailwind CSS 配置
├── 📄 wrangler.toml            # Cloudflare 配置
└── 📄 README.md                # 项目文档
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！

1. **Fork** 本项目
2. **创建** 特性分支 (`git checkout -b feature/AmazingFeature`)
3. **提交** 更改 (`git commit -m 'Add some AmazingFeature'`)
4. **推送** 到分支 (`git push origin feature/AmazingFeature`)
5. **创建** Pull Request

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

- [Next.js](https://nextjs.org/) - 强大的 React 框架
- [Tailwind CSS](https://tailwindcss.com/) - 优秀的 CSS 框架
- [react-simple-maps](https://www.react-simple-maps.io/) - 地图组件库
- [Recharts](https://recharts.org/) - React 图表库
- [Lucide](https://lucide.dev/) - 美观的图标库

---

<div align="center">

**如果这个项目对你有帮助，请给它一个 ⭐️**

Made with ❤️ by [wob-21](https://github.com/wob-21)

</div>

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源许可证。
MIT License

Copyright (c) 2025 wob

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```
