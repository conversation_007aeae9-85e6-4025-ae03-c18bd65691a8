import React, { useEffect, useState } from 'react';
import { ComposableMap, Geographies, Geography } from 'react-simple-maps';
import { ChinaData } from 'china-map-geojson';

interface PingResult {
  node: string;
  ping: number;
  status: 'success' | 'timeout' | 'error';
  timestamp: number;
}

interface ChinaMapProps {
  pingResults: PingResult[];
  isDarkMode: boolean;
  onProvinceClick?: (provinceName: string) => void;
}

// 省份名称映射（地图省份名 -> ping节点城市名）
const provinceNameMap: { [key: string]: string } = {
  // 直辖市（地图中可能显示为"北京市"或"北京"）
  '北京市': '北京',
  '北京': '北京',
  '天津市': '天津',
  '天津': '天津',
  '上海市': '上海',
  '上海': '上海',
  '重庆市': '重庆',
  '重庆': '重庆',

  // 省份（地图省份名 -> ping节点城市名）
  '河北省': '石家庄',
  '河北': '石家庄',
  '山西省': '太原',
  '山西': '太原',
  '辽宁省': '沈阳',
  '辽宁': '沈阳',
  '吉林省': '长春',
  '吉林': '长春',
  '黑龙江省': '哈尔滨',
  '黑龙江': '哈尔滨',
  '江苏省': '南京',
  '江苏': '南京',
  '浙江省': '杭州',
  '浙江': '杭州',
  '安徽省': '合肥',
  '安徽': '合肥',
  '福建省': '福州',
  '福建': '福州',
  '江西省': '南昌',
  '江西': '南昌',
  '山东省': '济南',
  '山东': '济南',
  '河南省': '郑州',
  '河南': '郑州',
  '湖北省': '武汉',
  '湖北': '武汉',
  '湖南省': '长沙',
  '湖南': '长沙',
  '广东省': '广州',
  '广东': '广州',
  '海南省': '海口',
  '海南': '海口',
  '四川省': '成都',
  '四川': '成都',
  '贵州省': '贵阳',
  '贵州': '贵阳',
  '云南省': '昆明',
  '云南': '昆明',
  '陕西省': '西安',
  '陕西': '西安',
  '甘肃省': '兰州',
  '甘肃': '兰州',
  '青海省': '西宁',
  '青海': '西宁',
  '台湾省': '台北',
  '台湾': '台北',

  // 自治区
  '内蒙古自治区': '呼和浩特',
  '内蒙古': '呼和浩特',
  '广西壮族自治区': '南宁',
  '广西': '南宁',
  '西藏自治区': '拉萨',
  '西藏': '拉萨',
  '宁夏回族自治区': '银川',
  '宁夏': '银川',
  '新疆维吾尔自治区': '乌鲁木齐',
  '新疆': '乌鲁木齐',

  // 特别行政区
  '香港特别行政区': '香港',
  '香港': '香港',
  '澳门特别行政区': '澳门',
  '澳门': '澳门'
};

const ChinaMap: React.FC<ChinaMapProps> = ({
  pingResults,
  isDarkMode,
  onProvinceClick
}) => {
  const [mapData, setMapData] = useState<any>(null);
  const [hoveredProvince, setHoveredProvince] = useState<string | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    // 加载中国地图数据
    setMapData(ChinaData);
  }, []);



  // 根据ping值获取颜色
  const getPingColor = (ping: number, status: string): string => {
    if (status !== 'success') return '#dc2626'; // 🔴 红色 - 超时
    if (ping <= 50) return '#16a34a';           // 🟢 深绿色 - ≤50ms
    if (ping <= 100) return '#22c55e';          // ✅ 绿色 - 51ms-100ms
    if (ping <= 200) return '#84cc16';          // 🟢 浅绿色 - 101ms-200ms
    if (ping <= 250) return '#eab308';          // 🟡 黄色 - 201ms-250ms
    return '#ea580c';                           // 🟠 橙色 - >250ms
  };

  // 获取省份的ping结果
  const getProvinceResult = (provinceName: string): PingResult | null => {
    if (pingResults.length === 0) return null;

    // 1. 尝试直接匹配省份名
    let result = pingResults.find(r => r.node === provinceName);
    if (result) return result;

    // 2. 尝试通过映射匹配（地图省份名 -> ping节点城市名）
    const mappedCityName = provinceNameMap[provinceName];
    if (mappedCityName) {
      result = pingResults.find(r => r.node === mappedCityName);
      if (result) return result;
    }

    // 3. 尝试模糊匹配（去掉省、市、自治区等后缀）
    const cleanProvinceName = provinceName.replace(/(省|市|自治区|特别行政区)$/, '');
    result = pingResults.find(r =>
      r.node.includes(cleanProvinceName) ||
      cleanProvinceName.includes(r.node) ||
      r.node === cleanProvinceName
    );
    if (result) return result;

    return null;
  };

  // 处理省份点击
  const handleProvinceClick = (geo: any) => {
    const provinceName = geo.properties.name;
    const result = getProvinceResult(provinceName);
    const displayName = provinceNameMap[provinceName] || provinceName;
    
    if (onProvinceClick) {
      onProvinceClick(displayName);
    }
  };

  if (!mapData) {
    return (
      <div className={`flex items-center justify-center h-96 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'} rounded-lg`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>加载中国地图...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative rounded-lg overflow-hidden ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
      {/* 标题 */}
      <div className={`px-6 py-4 border-b ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`}>
        <h3 className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          中国网络延迟地图
        </h3>
      </div>

      {/* 地图容器 */}
      <div className="relative">
        <ComposableMap
          projection="geoMercator"
          projectionConfig={{
            scale: 550,
            center: [105, 37]
          }}
          width={800}
          height={500}
          style={{
            width: '100%',
            height: 'auto'
          }}
          onMouseMove={(event) => {
            setMousePosition({ x: event.clientX, y: event.clientY });
          }}
        >
          <Geographies geography={mapData}>
            {({ geographies }) =>
              geographies.map((geo) => {
                const provinceName = geo.properties.name;
                const result = getProvinceResult(provinceName);
                const fillColor = result 
                  ? getPingColor(result.ping, result.status)
                  : (isDarkMode ? '#374151' : '#e5e7eb'); // 默认灰色

                return (
                  <Geography
                    key={geo.rsmKey}
                    geography={geo}
                    onClick={() => handleProvinceClick(geo)}
                    onMouseEnter={() => {
                      setHoveredProvince(provinceName);
                    }}
                    onMouseLeave={() => {
                      setHoveredProvince(null);
                    }}
                    style={{
                      default: {
                        fill: fillColor,
                        stroke: isDarkMode ? '#4b5563' : '#d1d5db',
                        strokeWidth: 0.5,
                        outline: 'none',
                      },
                      hover: {
                        fill: fillColor,
                        stroke: isDarkMode ? '#6b7280' : '#9ca3af',
                        strokeWidth: 1.5,
                        outline: 'none',
                        filter: 'brightness(1.1)',
                        cursor: 'pointer'
                      },
                      pressed: {
                        fill: fillColor,
                        stroke: isDarkMode ? '#6b7280' : '#9ca3af',
                        strokeWidth: 1,
                        outline: 'none',
                      },
                    }}
                  />
                );
              })
            }
          </Geographies>
        </ComposableMap>

        {/* 图例 */}
        <div className={`absolute bottom-4 left-4 p-4 rounded-lg shadow-lg ${isDarkMode ? 'bg-gray-900 bg-opacity-90' : 'bg-white bg-opacity-90'}`}>
          <h4 className={`text-sm font-bold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            延迟图例
          </h4>
          <div className="space-y-2 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded" style={{ backgroundColor: '#16a34a' }}></div>
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>≤50ms</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded" style={{ backgroundColor: '#22c55e' }}></div>
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>51-100ms</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded" style={{ backgroundColor: '#84cc16' }}></div>
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>101-200ms</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded" style={{ backgroundColor: '#eab308' }}></div>
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>201-250ms</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded" style={{ backgroundColor: '#ea580c' }}></div>
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>&gt;250ms</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded" style={{ backgroundColor: '#dc2626' }}></div>
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>超时</span>
            </div>
          </div>
        </div>

        {/* 悬停提示框 */}
        {hoveredProvince && (
          <div
            className={`fixed pointer-events-none z-50 px-3 py-2 rounded-lg shadow-lg ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'} border ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}
            style={{
              left: mousePosition.x + 10,
              top: mousePosition.y - 10,
              transform: 'translate(0, -100%)'
            }}
          >
            <div className="text-sm font-medium">
              {hoveredProvince}
              {(() => {
                const result = getProvinceResult(hoveredProvince);
                if (result) {
                  return (
                    <div className="text-xs mt-1" style={{ color: getPingColor(result.ping, result.status) }}>
                      {result.status === 'success' ? `${result.ping}ms` : '超时'}
                    </div>
                  );
                }
                return <div className="text-xs mt-1 text-gray-500">未测试</div>;
              })()}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChinaMap;
