// Vercel Edge Function 用于边缘网络测试
import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';

interface PingRequest {
  target: string;
  method?: 'http' | 'tcp';
  timeout?: number;
}

interface PingResponse {
  success: boolean;
  latency?: number;
  target: string;
  method: string;
  timestamp: number;
  error?: string;
  location?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: PingRequest = await request.json();
    const { target, method = 'http', timeout = 8000 } = body;

    if (!target) {
      return NextResponse.json(
        { 
          success: false, 
          error: '目标地址不能为空',
          target: '',
          method,
          timestamp: Date.now()
        },
        { status: 400 }
      );
    }

    // 执行网络测试
    const result = await performEdgeNetworkTest(target, method, timeout);
    
    // 添加Vercel边缘位置信息
    const vercelRegion = process.env.VERCEL_REGION || 'Unknown';
    result.location = vercelRegion;

    return NextResponse.json(result);
  } catch (error) {
    console.error('边缘网络测试失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        target: '',
        method: 'http',
        timestamp: Date.now()
      },
      { status: 500 }
    );
  }
}

// 执行边缘网络测试
async function performEdgeNetworkTest(target: string, method: string, timeout: number): Promise<PingResponse> {
  const startTime = Date.now();
  
  try {
    // 确保URL格式正确
    let testUrl = target;
    if (!testUrl.startsWith('http://') && !testUrl.startsWith('https://')) {
      testUrl = `https://${target}`;
    }

    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(testUrl, {
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; VercelEdgePingTool/1.0)',
      },
    });

    clearTimeout(timeoutId);
    const endTime = Date.now();
    const latency = endTime - startTime;

    if (response.ok || response.status < 500) {
      return {
        success: true,
        latency,
        target,
        method,
        timestamp: Date.now()
      };
    } else {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        target,
        method,
        timestamp: Date.now()
      };
    }
  } catch (error) {
    const endTime = Date.now();
    const latency = endTime - startTime;
    
    if (error instanceof Error && error.name === 'AbortError') {
      return {
        success: false,
        error: '请求超时',
        target,
        method,
        timestamp: Date.now()
      };
    }

    // 对于某些错误，我们仍然可以返回延迟时间
    if (latency < timeout && latency > 0) {
      return {
        success: true,
        latency,
        target,
        method,
        timestamp: Date.now()
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      target,
      method,
      timestamp: Date.now()
    };
  }
}

// 处理GET请求 - 健康检查
export async function GET() {
  const vercelRegion = process.env.VERCEL_REGION || 'Unknown';
  
  return NextResponse.json({
    status: 'ok',
    message: 'Vercel Edge Function Ping API is running',
    location: vercelRegion,
    timestamp: Date.now()
  });
}
