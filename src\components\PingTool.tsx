import React, { useState, useEffect } from 'react';
import ChinaMap from './ChinaMap';
import VisitCounter from './VisitCounter';
import { performGlobalpingTest } from '../utils/globalping';



interface PingToolProps {
  isDarkMode: boolean;
}

interface PingResult {
  node: string;
  ping: number;
  status: 'success' | 'timeout' | 'error';
  timestamp: number;
  method?: 'http' | 'globalping' | 'simulate';
  details?: {
    min?: number;
    max?: number;
    loss?: number;
    network?: string;
  };
}

const PingTool: React.FC<PingToolProps> = ({ isDarkMode }) => {
  const [target, setTarget] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [pingResults, setPingResults] = useState<PingResult[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'map'>('map');
  const [testMethod, setTestMethod] = useState<'mixed' | 'http' | 'globalping' | 'simulate'>('mixed');
  const [testProgress, setTestProgress] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [testStats, setTestStats] = useState<{
    total: number;
    success: number;
    failed: number;
    avgLatency: number;
  }>({ total: 0, success: 0, failed: 0, avgLatency: 0 });






  // 完整的中国省份城市测试节点列表
  const testNodes = [
    { name: '北京', province: '北京' },
    { name: '上海', province: '上海' },
    { name: '天津', province: '天津' },
    { name: '重庆', province: '重庆' },
    { name: '石家庄', province: '河北' },
    { name: '太原', province: '山西' },
    { name: '沈阳', province: '辽宁' },
    { name: '长春', province: '吉林' },
    { name: '哈尔滨', province: '黑龙江' },
    { name: '南京', province: '江苏' },
    { name: '杭州', province: '浙江' },
    { name: '合肥', province: '安徽' },
    { name: '福州', province: '福建' },
    { name: '南昌', province: '江西' },
    { name: '济南', province: '山东' },
    { name: '郑州', province: '河南' },
    { name: '武汉', province: '湖北' },
    { name: '长沙', province: '湖南' },
    { name: '广州', province: '广东' },
    { name: '海口', province: '海南' },
    { name: '成都', province: '四川' },
    { name: '贵阳', province: '贵州' },
    { name: '昆明', province: '云南' },
    { name: '西安', province: '陕西' },
    { name: '兰州', province: '甘肃' },
    { name: '西宁', province: '青海' },
    { name: '呼和浩特', province: '内蒙古' },
    { name: '南宁', province: '广西' },
    { name: '拉萨', province: '西藏' },
    { name: '银川', province: '宁夏' },
    { name: '乌鲁木齐', province: '新疆' },
    { name: '香港', province: '香港' },
    { name: '澳门', province: '澳门' },
    { name: '台北', province: '台湾' }
  ];

  // 根据ping值获取颜色（用于文字显示）
  const getPingColor = (ping: number, status: string) => {
    if (status !== 'success') return '#dc2626'; // 超时 - 红色
    if (ping <= 50) return '#16a34a';           // ≤50ms - 深绿
    if (ping <= 100) return '#22c55e';          // 51-100ms - 绿色
    if (ping <= 200) return '#84cc16';          // 101-200ms - 浅绿
    if (ping <= 250) return '#eab308';          // 201-250ms - 黄色
    return '#ea580c';                           // >250ms - 橙色
  };

  // 根据ping值获取背景色类名（用于卡片背景）
  const getPingBgClass = (ping: number, status: string) => {
    if (status !== 'success') return 'bg-red-600'; // 超时 - 红色背景
    if (ping <= 50) return 'bg-green-700';         // ≤50ms - 深绿背景
    if (ping <= 100) return 'bg-green-600';        // 51-100ms - 绿色背景
    if (ping <= 200) return 'bg-lime-500';         // 101-200ms - 浅绿背景
    if (ping <= 250) return 'bg-yellow-500';       // 201-250ms - 黄色背景
    return 'bg-orange-600';                        // >250ms - 橙色背景
  };

  // HTTP延迟测试函数 - 使用真实的HTTP请求测试延迟
  const httpLatencyTest = async (targetUrl: string): Promise<number> => {
    try {
      // 确保URL格式正确
      let testUrl = targetUrl;
      if (!testUrl.startsWith('http://') && !testUrl.startsWith('https://')) {
        testUrl = `https://${testUrl}`;
      }

      const startTime = performance.now();

      // 使用fetch进行HTTP请求，设置超时
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

      const response = await fetch(testUrl, {
        method: 'HEAD', // 使用HEAD请求减少数据传输
        mode: 'no-cors', // 避免CORS问题
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);
      const endTime = performance.now();

      return Math.round(endTime - startTime);
    } catch (error) {
      // 如果直接请求失败，尝试使用代理服务
      return await proxyLatencyTest(targetUrl);
    }
  };

  // 代理延迟测试 - 通过服务端API进行测试
  const proxyLatencyTest = async (targetUrl: string): Promise<number> => {
    try {
      const startTime = performance.now();

      // 优先尝试边缘函数
      let response = await fetch('/api/edge-ping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ target: targetUrl }),
      });

      // 如果边缘函数失败，回退到普通API
      if (!response.ok) {
        response = await fetch('/api/ping', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ target: targetUrl }),
        });
      }

      const endTime = performance.now();
      const result = await response.json();

      if (result.success && result.latency) {
        return result.latency;
      }

      // 如果服务端测试失败，返回HTTP请求的往返时间
      return Math.round(endTime - startTime);
    } catch (error) {
      throw new Error('网络测试失败');
    }
  };

  // Globalping测试函数
  const globalpingTest = async (targetUrl: string): Promise<PingResult[]> => {
    try {
      setTestProgress('正在通过全球节点进行真实ping测试...');
      const results = await performGlobalpingTest(targetUrl);

      return results.map(result => ({
        node: result.location,
        ping: result.ping,
        status: result.status,
        timestamp: Date.now(),
        method: 'globalping' as const,
        details: {
          min: result.min,
          max: result.max,
          loss: result.loss,
          network: result.network
        }
      }));
    } catch (error) {
      console.error('Globalping测试失败:', error);
      throw error;
    }
  };

  // 真实ping测试函数
  const realPingTest = async (nodeInfo: {name: string, province: string}, targetUrl: string): Promise<PingResult> => {
    try {
      const latency = await httpLatencyTest(targetUrl);

      return {
        node: nodeInfo.name,
        ping: latency,
        status: latency > 0 ? 'success' : 'timeout',
        timestamp: Date.now(),
        method: 'http'
      };
    } catch (error) {
      // 如果测试失败，回退到模拟测试
      return await simulatePing(nodeInfo, targetUrl);
    }
  };

  // 模拟ping测试函数 - 作为备用方案
  const simulatePing = async (nodeInfo: {name: string, province: string}, targetUrl: string): Promise<PingResult> => {
    return new Promise((resolve) => {
      // 模拟网络延迟
      const delay = Math.random() * 1000 + 200; // 200-1200ms

      setTimeout(() => {
        // 检查目标URL是否为被墙网站
        const blockedDomains = [
          'chatgpt.com',
          'openai.com',
          'google.com',
          'youtube.com',
          'facebook.com',
          'twitter.com',
          'instagram.com'
        ];

        const isBlocked = blockedDomains.some(domain => targetUrl.includes(domain));

        if (isBlocked) {
          // 被墙网站在中国大陆应该全部超时
          if (['香港', '澳门', '台北'].includes(nodeInfo.name)) {
            const latency = Math.random() * 100 + 150; // 150-250ms
            resolve({
              node: nodeInfo.name,
              ping: Math.round(latency),
              status: 'success',
              timestamp: Date.now()
            });
          } else {
            resolve({
              node: nodeInfo.name,
              ping: 0,
              status: 'timeout',
              timestamp: Date.now()
            });
          }
          return;
        }

        // 对于非被墙网站，模拟正常的网络状况
        const baseLatency = Math.random() * 150 + 20; // 20-170ms基础延迟

        // 根据地区调整延迟
        let adjustedLatency = baseLatency;
        if (['北京', '上海', '广州', '深圳', '杭州', '南京'].includes(nodeInfo.name)) {
          adjustedLatency *= 0.8;
        } else if (['乌鲁木齐', '拉萨', '西宁', '兰州', '银川'].includes(nodeInfo.name)) {
          adjustedLatency *= 1.3;
        }

        resolve({
          node: nodeInfo.name,
          ping: Math.round(adjustedLatency),
          status: 'success',
          timestamp: Date.now(),
          method: 'simulate'
        });
      }, delay);
    });
  };

  // 计算测试统计信息
  const calculateStats = (results: PingResult[]) => {
    const total = results.length;
    const success = results.filter(r => r.status === 'success').length;
    const failed = total - success;
    const successResults = results.filter(r => r.status === 'success');
    const avgLatency = successResults.length > 0
      ? Math.round(successResults.reduce((sum, r) => sum + r.ping, 0) / successResults.length)
      : 0;

    setTestStats({ total, success, failed, avgLatency });
  };

  const startPing = async () => {
    if (!target.trim()) return;
    setIsRunning(true);
    setPingResults([]);
    setTestProgress('');
    setErrorMessage('');
    setTestStats({ total: 0, success: 0, failed: 0, avgLatency: 0 });

    try {
      let allResults: PingResult[] = [];

      if (testMethod === 'globalping') {
        // 使用Globalping进行真实测试
        try {
          const globalpingResults = await globalpingTest(target);
          allResults = globalpingResults;
          setTestProgress('Globalping测试完成');
        } catch (error) {
          setTestProgress('Globalping测试失败，回退到HTTP测试');
          // 回退到HTTP测试
          const httpPromises = testNodes.slice(0, 5).map(nodeInfo => realPingTest(nodeInfo, target));
          const httpResults = await Promise.allSettled(httpPromises);
          allResults = httpResults
            .filter(result => result.status === 'fulfilled')
            .map(result => (result as PromiseFulfilledResult<PingResult>).value);
        }
      } else if (testMethod === 'http') {
        // 仅使用HTTP测试
        setTestProgress('正在进行HTTP延迟测试...');
        const httpPromises = testNodes.map(nodeInfo => realPingTest(nodeInfo, target));
        const httpResults = await Promise.allSettled(httpPromises);
        allResults = httpResults
          .filter(result => result.status === 'fulfilled')
          .map(result => (result as PromiseFulfilledResult<PingResult>).value);
      } else if (testMethod === 'simulate') {
        // 仅使用模拟测试
        setTestProgress('正在进行模拟测试...');
        const simulatePromises = testNodes.map(nodeInfo => simulatePing(nodeInfo, target));
        allResults = await Promise.all(simulatePromises);
      } else {
        // 混合模式：先尝试Globalping，然后HTTP，最后模拟
        try {
          setTestProgress('尝试Globalping真实测试...');
          const globalpingResults = await globalpingTest(target);
          allResults.push(...globalpingResults);
          setTestProgress('Globalping测试完成，补充HTTP测试...');
        } catch (error) {
          console.warn('Globalping测试失败:', error);
          setTestProgress('Globalping失败，使用HTTP测试...');
        }

        // 补充HTTP测试
        const httpPromises = testNodes.slice(0, Math.max(5, 10 - allResults.length)).map(nodeInfo => realPingTest(nodeInfo, target));
        const httpResults = await Promise.allSettled(httpPromises);
        const successfulHttpResults = httpResults
          .filter(result => result.status === 'fulfilled')
          .map(result => (result as PromiseFulfilledResult<PingResult>).value);
        allResults.push(...successfulHttpResults);

        // 如果结果不足，用模拟测试补充
        if (allResults.length < testNodes.length) {
          setTestProgress('补充模拟测试数据...');
          const remainingNodes = testNodes.slice(allResults.length);
          const simulatePromises = remainingNodes.map(nodeInfo => simulatePing(nodeInfo, target));
          const simulateResults = await Promise.all(simulatePromises);
          allResults.push(...simulateResults);
        }
      }

      // 如果没有任何结果，使用完全模拟模式
      if (allResults.length === 0) {
        setTestProgress('所有测试失败，使用模拟数据...');
        const fallbackPromises = testNodes.map(nodeInfo => simulatePing(nodeInfo, target));
        allResults = await Promise.all(fallbackPromises);
      }

      const sortedResults = allResults.sort((a, b) => a.ping - b.ping);
      setPingResults(sortedResults);
      updateMapColors(sortedResults);
      calculateStats(sortedResults);
      setTestProgress('测试完成');
    } catch (error) {
      console.error('Ping测试失败:', error);
      setTestProgress('测试失败');
      setErrorMessage(error instanceof Error ? error.message : '未知错误');

      // 最终回退
      try {
        const fallbackPromises = testNodes.map(nodeInfo => simulatePing(nodeInfo, target));
        const fallbackResults = await Promise.all(fallbackPromises);
        const sortedFallback = fallbackResults.sort((a, b) => a.ping - b.ping);
        setPingResults(sortedFallback);
        updateMapColors(sortedFallback);
        calculateStats(sortedFallback);
        setTestProgress('使用模拟数据');
      } catch (fallbackError) {
        setErrorMessage('所有测试方法都失败了');
      }
    } finally {
      setIsRunning(false);
      setTimeout(() => {
        setTestProgress('');
        setErrorMessage('');
      }, 5000); // 5秒后清除状态信息
    }
  };

  const stopPing = () => {
    setIsRunning(false);
  };

  // 更新显示（现在不需要地图更新）
  const updateMapColors = (results: PingResult[]) => {
    // 省份网格会自动根据pingResults更新，不需要额外操作
    console.log('测试结果更新，共', results.length, '个节点');
  };

  useEffect(() => {
    console.log('省份网格显示已初始化，包含', testNodes.length, '个测试节点');
  }, []);

  return (
    <div className={`rounded-lg shadow-sm p-8 mb-8 transition-colors duration-300 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
      {/* 头部区域 - 包含标题和计数器 */}
      <div className="relative flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
        {/* 左侧标题区域 */}
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center shadow-lg">
            <span className="text-white text-lg font-bold">P</span>
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-3">
            <h3 className={`text-2xl font-semibold transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Ping 工具
            </h3>
            <span className={`text-lg transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} hidden sm:inline`}>
              - 网络连通性测试
            </span>
          </div>
        </div>

        {/* 右侧计数器 */}
        <div className="flex justify-end sm:justify-start">
          <VisitCounter isDarkMode={isDarkMode} />
        </div>
      </div>

      {/* 输入控制区域 */}
      <div className="mb-6">
        {/* 测试方法选择器 */}
        <div className="mb-4">
          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            测试方法：
          </label>
          <div className="flex gap-2 flex-wrap">
            {[
              { value: 'mixed', label: '智能混合', desc: 'Globalping + HTTP + 模拟' },
              { value: 'globalping', label: '真实Ping', desc: '全球节点真实ping' },
              { value: 'http', label: 'HTTP测试', desc: 'HTTP延迟测试' },
              { value: 'simulate', label: '模拟测试', desc: '模拟网络延迟' }
            ].map(method => (
              <button
                key={method.value}
                onClick={() => setTestMethod(method.value as any)}
                disabled={isRunning}
                className={`px-3 py-2 text-sm rounded-lg border transition-colors duration-300 ${
                  testMethod === method.value
                    ? isDarkMode
                      ? 'bg-blue-600 border-blue-600 text-white'
                      : 'bg-blue-500 border-blue-500 text-white'
                    : isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                } ${isRunning ? 'opacity-50 cursor-not-allowed' : ''}`}
                title={method.desc}
              >
                {method.label}
              </button>
            ))}
          </div>
        </div>

        <div className="flex space-x-3 mb-4">
          <input
            type="text"
            value={target}
            onChange={(e) => setTarget(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && target.trim() && !isRunning) {
                startPing();
              }
            }}
            placeholder="请输入域名，例如：example.com，8.8.8.8 (按 Enter 开始)"
            className={`flex-1 px-4 py-3 text-lg border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-300 ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
            }`}
            disabled={isRunning}
          />
          <button
            onClick={isRunning ? stopPing : startPing}
            disabled={!target.trim()}
            className={`px-6 py-3 text-lg rounded-lg font-medium transition-colors duration-300 ${
              isRunning
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : target.trim()
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isRunning ? '停止' : '开始'}
          </button>
        </div>

        {/* 测试进度显示 */}
        {(isRunning || testProgress) && (
          <div className={`mb-4 p-3 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-blue-50'}`}>
            <div className="flex items-center gap-2">
              {isRunning && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
              )}
              <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-blue-700'}`}>
                {testProgress || '正在测试...'}
              </span>
            </div>
          </div>
        )}

        {/* 错误信息显示 */}
        {errorMessage && (
          <div className={`mb-4 p-3 rounded-lg ${isDarkMode ? 'bg-red-900 border-red-700' : 'bg-red-50 border-red-200'} border`}>
            <div className="flex items-center gap-2">
              <span className="text-red-500">⚠️</span>
              <span className={`text-sm ${isDarkMode ? 'text-red-300' : 'text-red-700'}`}>
                {errorMessage}
              </span>
            </div>
          </div>
        )}

        {/* 测试统计信息 */}
        {pingResults.length > 0 && (
          <div className={`mb-4 p-3 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-green-50'}`}>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className={`font-bold text-lg ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {testStats.total}
                </div>
                <div className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  总节点
                </div>
              </div>
              <div className="text-center">
                <div className="font-bold text-lg text-green-600">
                  {testStats.success}
                </div>
                <div className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  成功
                </div>
              </div>
              <div className="text-center">
                <div className="font-bold text-lg text-red-600">
                  {testStats.failed}
                </div>
                <div className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  失败
                </div>
              </div>
              <div className="text-center">
                <div className={`font-bold text-lg ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                  {testStats.avgLatency}ms
                </div>
                <div className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  平均延迟
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 视图切换按钮 */}
        <div className="flex space-x-2">
          <button
            onClick={() => setViewMode('map')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors duration-300 ${
              viewMode === 'map'
                ? 'bg-blue-600 text-white'
                : isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            🗺️ 地图视图
          </button>
          <button
            onClick={() => setViewMode('grid')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors duration-300 ${
              viewMode === 'grid'
                ? 'bg-blue-600 text-white'
                : isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            📊 网格视图
          </button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 左侧：视图区域 - 占2/3宽度 */}
        <div className="lg:col-span-2">
          {viewMode === 'map' ? (
            /* 地图视图 */
            <ChinaMap
              pingResults={pingResults}
              isDarkMode={isDarkMode}
              onProvinceClick={(provinceName) => {
                console.log('点击省份:', provinceName);
              }}
            />
          ) : (
            /* 网格视图 */
            <div className={`relative rounded-lg overflow-hidden transition-colors duration-300 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`} style={{ height: '600px' }}>
              <div className="p-4 h-full flex flex-col">
                <h4 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  中国网络延迟网格
                </h4>

                <div className="flex-1 grid grid-cols-6 gap-3 p-4 max-h-full overflow-y-auto">
                  {testNodes.map((nodeInfo, index) => {
                    const result = pingResults.find(r => r.node === nodeInfo.name);
                    const bgColor = result ? getPingBgClass(result.ping, result.status) : (isDarkMode ? 'bg-gray-600' : 'bg-gray-200');

                    return (
                      <div
                        key={index}
                        className={`${bgColor} rounded-lg p-3 text-center transition-all duration-300 hover:scale-105 shadow-lg`}
                        style={{
                          minHeight: '80px',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          alignItems: 'center'
                        }}
                      >
                        <div className="text-sm font-bold text-white mb-1">
                          {nodeInfo.name}
                        </div>
                        <div className="text-xs text-white font-medium">
                          {result ? (
                            result.status === 'success' ? `${result.ping}ms` : '超时'
                          ) : '未测试'}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* 颜色图例 */}
              <div className="p-4 border-t" style={{ height: '100px' }}>
                <h5 className={`text-sm font-bold mb-3 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  延迟等级
                </h5>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#16a34a' }}></div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>≤50ms</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#22c55e' }}></div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>51ms-100ms</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#84cc16' }}></div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>101ms-200ms</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#eab308' }}></div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>201ms-250ms</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#ea580c' }}></div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>&gt;250ms</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 rounded" style={{ backgroundColor: '#dc2626' }}></div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>超时</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 右侧：数据表格 - 占1/3宽度 */}
        <div className="lg:col-span-1">
          <div className={`rounded-lg p-6 transition-colors duration-300 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`} style={{ height: '600px', display: 'flex', flexDirection: 'column' }}>
            <h4 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              测试结果
            </h4>
            <div className="flex-1 overflow-y-auto space-y-3 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-200">
              <div className={`grid grid-cols-4 gap-3 font-medium text-sm pb-2 border-b border-gray-300 sticky top-0 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <span className={`transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>节点</span>
                <span className={`transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>延迟</span>
                <span className={`transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>状态</span>
                <span className={`transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>方法</span>
              </div>

              {isRunning && pingResults.length === 0 && (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>正在测试中...</p>
                  </div>
                </div>
              )}

              {pingResults.length > 0 ? (
                pingResults.map((result, index) => (
                  <div key={index} className={`grid grid-cols-4 gap-3 py-2 px-2 rounded-md transition-all duration-300 hover:scale-105 cursor-pointer ${isDarkMode ? 'hover:bg-gray-600' : 'hover:bg-gray-200'}`}>
                    <span className={`text-sm font-medium transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {result.node}
                    </span>
                    <span className="text-sm font-semibold" style={{
                      color: getPingColor(result.ping, result.status)
                    }}>
                      {result.status === 'success' ? `${result.ping}ms` : '超时'}
                    </span>
                    <span className="text-sm font-medium" style={{
                      color: getPingColor(result.ping, result.status)
                    }}>
                      {result.status === 'success' ? '正常' : '超时'}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      result.method === 'globalping'
                        ? 'bg-green-100 text-green-800'
                        : result.method === 'http'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                    }`}>
                      {result.method === 'globalping' ? '真实' : result.method === 'http' ? 'HTTP' : '模拟'}
                    </span>
                  </div>
                ))
              ) : !isRunning && (
                <div className="flex items-center justify-center h-full">
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    请输入域名或IP地址开始测试
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PingTool;
