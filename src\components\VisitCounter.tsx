import React, { useState, useEffect } from 'react';

interface VisitCounterProps {
  isDarkMode: boolean;
}

const VisitCounter: React.FC<VisitCounterProps> = ({ isDarkMode }) => {
  const [visitCount, setVisitCount] = useState<number>(0);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);


  useEffect(() => {
    // 从localStorage获取访问次数
    const storedCount = localStorage.getItem('ping-tool-visit-count');
    const currentCount = storedCount ? parseInt(storedCount, 10) : 0;

    // 增加访问次数
    const newCount = currentCount + 1;
    setVisitCount(newCount);
    localStorage.setItem('ping-tool-visit-count', newCount.toString());

    // 触发动画
    setIsAnimating(true);
    const timer = setTimeout(() => setIsAnimating(false), 1500);

    return () => clearTimeout(timer);
  }, []);

  // 格式化数字显示（添加千位分隔符）
  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  // 重置计数器（双击触发）
  const handleDoubleClick = () => {
    setVisitCount(1);
    localStorage.setItem('ping-tool-visit-count', '1');
    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), 1000);
  };

  return (
    <div
      className={`relative inline-flex items-center px-3 py-2 rounded-lg shadow-md transition-all duration-300 hover:shadow-lg cursor-pointer select-none ${
        isDarkMode
          ? 'bg-gray-800 border border-gray-600'
          : 'bg-white border border-gray-200'
      } ${isAnimating ? 'scale-105' : 'scale-100'} hover:scale-102`}
      onDoubleClick={handleDoubleClick}
      title="双击重置计数器"
    >
      {/* 简洁的文本格式 */}
      <span className={`text-sm font-medium ${
        isDarkMode ? 'text-gray-300' : 'text-gray-600'
      }`}>
        访问次数：
      </span>
      <span className={`ml-1 text-sm font-bold ${
        isDarkMode ? 'text-blue-400' : 'text-blue-600'
      } ${isAnimating ? 'animate-pulse' : ''} transition-all duration-300`}>
        {formatNumber(visitCount)}
      </span>

      {/* 简化的状态指示器 */}
      <div className={`ml-2 w-2 h-2 rounded-full ${
        isDarkMode ? 'bg-green-400' : 'bg-green-500'
      } ${isAnimating ? 'animate-ping' : ''}`}></div>

      {/* 轻微的光效 */}
      {isAnimating && (
        <div className="absolute inset-0 rounded-lg bg-blue-400/10 animate-pulse"></div>
      )}
    </div>
  );
};

export default VisitCounter;
