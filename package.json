{"name": "ping-network-monitor", "version": "1.0.0", "description": "现代化的网络延迟测试工具，提供中国地图可视化的网络连通性监控", "author": "wob", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/wob-21/ping.git"}, "keywords": ["ping", "network", "monitoring", "china-map", "visualization", "nextjs", "react", "typescript"], "private": false, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:static": "BUILD_MODE=static next build", "build:vercel": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"china-map-geojson": "^1.0.4", "echarts": "^5.6.0", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-simple-maps": "^3.0.0", "recharts": "^3.0.2"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.12", "@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-simple-maps": "^3.0.6", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5"}}