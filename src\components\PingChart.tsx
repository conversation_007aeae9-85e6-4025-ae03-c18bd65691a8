'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, <PERSON><PERSON>hart, Bar } from 'recharts';

interface NetworkNode {
  id: string;
  name: string;
  location: string;
  status: 'online' | 'offline' | 'warning';
  ping: number;
  uptime: number;
  x: number;
  y: number;
}

interface PingChartProps {
  nodes: NetworkNode[];
  isDarkMode?: boolean;
}

interface PingData {
  time: string;
  [key: string]: string | number;
}

const PingChart = ({ nodes, isDarkMode = false }: PingChartProps) => {
  const [pingHistory, setPingHistory] = useState<PingData[]>([]);
  const [chartType, setChartType] = useState<'line' | 'bar'>('line');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    const generateInitialData = () => {
      const data: PingData[] = [];
      const now = new Date();
      
      for (let i = 23; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 5 * 60 * 1000);
        const dataPoint: PingData = {
          time: time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
        };
        
        nodes.forEach(node => {
          if (node.status !== 'offline') {
            dataPoint[node.location] = Math.max(5, node.ping + Math.floor(Math.random() * 20) - 10);
          }
        });
        
        data.push(dataPoint);
      }
      
      setPingHistory(data);
    };

    generateInitialData();
  }, [nodes]);

  useEffect(() => {
    if (!mounted) return;
    
    const interval = setInterval(() => {
      setPingHistory(prev => {
        const newData = [...prev];
        const now = new Date();
        const newDataPoint: PingData = {
          time: now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
        };
        
        nodes.forEach(node => {
          if (node.status !== 'offline') {
            newDataPoint[node.location] = Math.max(5, node.ping + Math.floor(Math.random() * 10) - 5);
          }
        });
        
        newData.push(newDataPoint);
        
        if (newData.length > 24) {
          newData.shift();
        }
        
        return newData;
      });
    }, 5000);

    return () => clearInterval(interval);
  }, [nodes, mounted]);

  const colors = [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
    '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
  ];

  const onlineNodes = nodes.filter(node => node.status !== 'offline');

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className={`p-3 border rounded-lg shadow-lg transition-colors duration-300 ${
          isDarkMode
            ? 'bg-gray-800 border-gray-600'
            : 'bg-white border-gray-200'
        }`}>
          <p className={`text-sm font-medium transition-colors duration-300 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>{`时间: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.dataKey}: ${entry.value}ms`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (!mounted) {
    return (
      <div className={`rounded-lg shadow-sm p-6 transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      }`}>
        <div className="flex items-center justify-center h-80">
          <div className={`transition-colors duration-300 ${
            isDarkMode ? 'text-gray-400' : 'text-gray-500'
          }`}>加载图表中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`rounded-lg shadow-sm p-8 transition-colors duration-300 ${
      isDarkMode ? 'bg-gray-800' : 'bg-white'
    }`}>
      <div className="flex items-center justify-between mb-8">
        <h2 className={`text-2xl font-semibold transition-colors duration-300 ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>网络延迟趋势</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setChartType('line')}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              chartType === 'line'
                ? 'bg-blue-100 text-blue-700 border border-blue-200'
                : isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            折线图
          </button>
          <button
            onClick={() => setChartType('bar')}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              chartType === 'bar'
                ? 'bg-blue-100 text-blue-700 border border-blue-200'
                : isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            柱状图
          </button>
        </div>
      </div>

      <div className="h-96">
        <ResponsiveContainer width="100%" height="100%">
          {chartType === 'line' ? (
            <LineChart data={pingHistory} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="time"
                stroke={isDarkMode ? '#9CA3AF' : '#6b7280'}
                fontSize={12}
                tick={{ fill: isDarkMode ? '#9CA3AF' : '#6b7280' }}
              />
              <YAxis
                stroke={isDarkMode ? '#9CA3AF' : '#6b7280'}
                fontSize={12}
                tick={{ fill: isDarkMode ? '#9CA3AF' : '#6b7280' }}
                label={{
                  value: '延迟 (ms)',
                  angle: -90,
                  position: 'insideLeft',
                  style: { textAnchor: 'middle', fill: isDarkMode ? '#9CA3AF' : '#6b7280' }
                }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              {onlineNodes.slice(0, 10).map((node, index) => (
                <Line
                  key={node.id}
                  type="monotone"
                  dataKey={node.location}
                  stroke={colors[index % colors.length]}
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  activeDot={{ r: 5 }}
                />
              ))}
            </LineChart>
          ) : (
            <BarChart data={pingHistory.slice(-6)} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="time"
                stroke={isDarkMode ? '#9CA3AF' : '#6b7280'}
                fontSize={12}
                tick={{ fill: isDarkMode ? '#9CA3AF' : '#6b7280' }}
              />
              <YAxis
                stroke={isDarkMode ? '#9CA3AF' : '#6b7280'}
                fontSize={12}
                tick={{ fill: isDarkMode ? '#9CA3AF' : '#6b7280' }}
                label={{
                  value: '延迟 (ms)',
                  angle: -90,
                  position: 'insideLeft',
                  style: { textAnchor: 'middle', fill: isDarkMode ? '#9CA3AF' : '#6b7280' }
                }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              {onlineNodes.slice(0, 5).map((node, index) => (
                <Bar
                  key={node.id}
                  dataKey={node.location}
                  fill={colors[index % colors.length]}
                  opacity={0.8}
                />
              ))}
            </BarChart>
          )}
        </ResponsiveContainer>
      </div>

      <div className={`mt-4 pt-4 border-t transition-colors duration-300 ${
        isDarkMode ? 'border-gray-600' : 'border-gray-200'
      }`}>
        <div className="flex flex-wrap gap-4">
          {onlineNodes.slice(0, 10).map((node, index) => (
            <div key={node.id} className="flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: colors[index % colors.length] }}
              ></div>
              <span className={`text-sm transition-colors duration-300 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>{node.location}</span>
              <span className={`text-xs transition-colors duration-300 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>({node.ping}ms)</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PingChart;
