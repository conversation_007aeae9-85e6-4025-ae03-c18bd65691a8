// Globalping API 集成工具
// 文档: https://github.com/jsdelivr/globalping

interface GlobalpingProbe {
  country: string;
  city: string;
  asn: number;
  network: string;
  tags: string[];
}

interface GlobalpingMeasurement {
  id: string;
  type: 'ping' | 'traceroute' | 'dns' | 'http';
  status: 'in-progress' | 'finished' | 'failed';
  target: string;
  probesCount: number;
  results: GlobalpingResult[];
}

interface GlobalpingResult {
  probe: GlobalpingProbe;
  result: {
    status: 'finished' | 'failed';
    rawOutput?: string;
    stats?: {
      min: number;
      max: number;
      avg: number;
      loss: number;
    };
  };
}

interface GlobalpingRequest {
  type: 'ping';
  target: string;
  locations?: {
    country?: string;
    city?: string;
    asn?: number;
    tags?: string[];
    limit?: number;
  }[];
  measurementOptions?: {
    packets?: number;
  };
}

const GLOBALPING_API_BASE = 'https://api.globalping.io/v1';

// 创建ping测试
export async function createGlobalpingTest(target: string, locations?: string[]): Promise<string> {
  const requestBody: GlobalpingRequest = {
    type: 'ping',
    target,
    measurementOptions: {
      packets: 3 // 减少包数量以提高速度
    }
  };

  // 如果指定了位置，添加位置过滤
  if (locations && locations.length > 0) {
    requestBody.locations = locations.map(location => ({
      country: getCountryCode(location),
      limit: 1
    }));
  } else {
    // 默认使用一些主要国家/地区
    requestBody.locations = [
      { country: 'CN', limit: 3 }, // 中国
      { country: 'US', limit: 2 }, // 美国
      { country: 'JP', limit: 1 }, // 日本
      { country: 'SG', limit: 1 }, // 新加坡
      { country: 'DE', limit: 1 }, // 德国
      { country: 'GB', limit: 1 }  // 英国
    ];
  }

  try {
    const response = await fetch(`${GLOBALPING_API_BASE}/measurements`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Globalping API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.id;
  } catch (error) {
    console.error('创建Globalping测试失败:', error);
    throw error;
  }
}

// 获取测试结果
export async function getGlobalpingResults(measurementId: string): Promise<GlobalpingMeasurement> {
  try {
    const response = await fetch(`${GLOBALPING_API_BASE}/measurements/${measurementId}`);
    
    if (!response.ok) {
      throw new Error(`Globalping API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('获取Globalping结果失败:', error);
    throw error;
  }
}

// 等待测试完成并获取结果
export async function waitForGlobalpingResults(measurementId: string, maxWaitTime = 30000): Promise<GlobalpingMeasurement> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    const measurement = await getGlobalpingResults(measurementId);
    
    if (measurement.status === 'finished' || measurement.status === 'failed') {
      return measurement;
    }
    
    // 等待2秒后重试
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  throw new Error('Globalping测试超时');
}

// 解析ping结果
export function parseGlobalpingResults(measurement: GlobalpingMeasurement) {
  return measurement.results.map(result => {
    const location = `${result.probe.city}, ${result.probe.country}`;
    
    if (result.result.status === 'failed' || !result.result.stats) {
      return {
        location,
        ping: 0,
        status: 'timeout' as const,
        country: result.probe.country,
        city: result.probe.city,
        network: result.probe.network
      };
    }

    return {
      location,
      ping: Math.round(result.result.stats.avg),
      status: 'success' as const,
      country: result.probe.country,
      city: result.probe.city,
      network: result.probe.network,
      min: result.result.stats.min,
      max: result.result.stats.max,
      loss: result.result.stats.loss
    };
  });
}

// 将城市名转换为国家代码
function getCountryCode(location: string): string {
  const countryMap: { [key: string]: string } = {
    '北京': 'CN', '上海': 'CN', '广州': 'CN', '深圳': 'CN',
    '杭州': 'CN', '南京': 'CN', '成都': 'CN', '重庆': 'CN',
    '天津': 'CN', '武汉': 'CN', '西安': 'CN', '沈阳': 'CN',
    '大连': 'CN', '青岛': 'CN', '济南': 'CN', '郑州': 'CN',
    '长沙': 'CN', '福州': 'CN', '厦门': 'CN', '昆明': 'CN',
    '贵阳': 'CN', '南宁': 'CN', '海口': 'CN', '三亚': 'CN',
    '石家庄': 'CN', '太原': 'CN', '呼和浩特': 'CN', '长春': 'CN',
    '哈尔滨': 'CN', '南昌': 'CN', '合肥': 'CN', '乌鲁木齐': 'CN',
    '拉萨': 'CN', '西宁': 'CN', '兰州': 'CN', '银川': 'CN',
    '香港': 'HK', '澳门': 'MO', '台北': 'TW'
  };
  
  return countryMap[location] || 'CN';
}

// 组合使用：创建测试并等待结果
export async function performGlobalpingTest(target: string, locations?: string[]) {
  try {
    const measurementId = await createGlobalpingTest(target, locations);
    const measurement = await waitForGlobalpingResults(measurementId);
    return parseGlobalpingResults(measurement);
  } catch (error) {
    console.error('Globalping测试失败:', error);
    throw error;
  }
}
